#include "Argon2idHasher.h"
#include <iostream>
#include <string>

int main() {
    std::cout << "=== Argon2id Hasher Demo ===" << std::endl;
    
    try {
        // Check if Argon2 library is available
        if (!Argon2idHasher::isAvailable()) {
            std::cerr << "Error: Argon2 library is not available!" << std::endl;
            return 1;
        }
        
        std::cout << "Argon2 library is available and working." << std::endl;
        
        // Create hasher with default configuration
        Argon2idHasher hasher;
        
        // Display current configuration
        auto config = hasher.getConfig();
        std::cout << "\nCurrent Configuration:" << std::endl;
        std::cout << "  Memory Cost: " << config.memory_cost << " KiB" << std::endl;
        std::cout << "  Time Cost: " << config.time_cost << " iterations" << std::endl;
        std::cout << "  Parallelism: " << config.parallelism << " threads" << std::endl;
        std::cout << "  Salt Length: " << config.salt_length << " bytes" << std::endl;
        std::cout << "  Hash Length: " << config.hash_length << " bytes" << std::endl;
        
        // Test password hashing
        std::string password = "MySecurePassword123!";
        std::cout << "\nHashing password: \"" << password << "\"" << std::endl;
        
        std::string hash = hasher.hash(password);
        std::cout << "Generated hash: " << hash << std::endl;
        
        // Test password verification
        std::cout << "\nVerifying password..." << std::endl;
        bool is_valid = hasher.verify(password, hash);
        std::cout << "Password verification: " << (is_valid ? "SUCCESS" : "FAILED") << std::endl;
        
        // Test with wrong password
        std::string wrong_password = "WrongPassword";
        std::cout << "\nTesting with wrong password: \"" << wrong_password << "\"" << std::endl;
        bool is_wrong_valid = hasher.verify(wrong_password, hash);
        std::cout << "Wrong password verification: " << (is_wrong_valid ? "SUCCESS (ERROR!)" : "FAILED (Expected)") << std::endl;
        
        // Test salt generation
        std::cout << "\nTesting salt generation..." << std::endl;
        auto salt1 = Argon2idHasher::generateSalt(16);
        auto salt2 = Argon2idHasher::generateSalt(16);
        
        std::cout << "Salt 1 (hex): ";
        for (uint8_t byte : salt1) {
            printf("%02x", byte);
        }
        std::cout << std::endl;
        
        std::cout << "Salt 2 (hex): ";
        for (uint8_t byte : salt2) {
            printf("%02x", byte);
        }
        std::cout << std::endl;
        
        bool salts_different = (salt1 != salt2);
        std::cout << "Salts are different: " << (salts_different ? "YES (Good)" : "NO (Bad)") << std::endl;
        
        // Test with custom configuration
        std::cout << "\nTesting with custom configuration..." << std::endl;
        Argon2Config custom_config(2048, 2, 2, 16, 32);  // Faster config for demo
        Argon2idHasher custom_hasher(custom_config);
        
        std::string custom_hash = custom_hasher.hash(password);
        std::cout << "Custom config hash: " << custom_hash << std::endl;
        
        bool custom_valid = custom_hasher.verify(password, custom_hash);
        std::cout << "Custom config verification: " << (custom_valid ? "SUCCESS" : "FAILED") << std::endl;
        
        // Test that different configs produce different hashes
        bool hashes_different = (hash != custom_hash);
        std::cout << "Different configs produce different hashes: " << (hashes_different ? "YES (Expected)" : "NO (Unexpected)") << std::endl;
        
        std::cout << "\n=== Demo completed successfully! ===" << std::endl;
        
    } catch (const Argon2Exception& e) {
        std::cerr << "Argon2 Error: " << e.what() << std::endl;
        return 1;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
