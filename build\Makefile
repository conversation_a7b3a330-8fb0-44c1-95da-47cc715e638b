# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\Argon2idDemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\Argon2idDemo\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	D:\msys64\mingw64\bin\ctest.exe $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	D:\msys64\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	D:\msys64\mingw64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\msys64\mingw64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\msys64\mingw64\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\msys64\mingw64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles E:\workspace\vscode\Argon2idDemo\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named argon2_static

# Build rule for target.
argon2_static: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 argon2_static
.PHONY : argon2_static

# fast build rule for target.
argon2_static/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/build
.PHONY : argon2_static/fast

#=============================================================================
# Target rules for targets named Argon2idHasher

# Build rule for target.
Argon2idHasher: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Argon2idHasher
.PHONY : Argon2idHasher

# fast build rule for target.
Argon2idHasher/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Argon2idHasher.dir\build.make CMakeFiles/Argon2idHasher.dir/build
.PHONY : Argon2idHasher/fast

#=============================================================================
# Target rules for targets named test_argon2id

# Build rule for target.
test_argon2id: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 test_argon2id
.PHONY : test_argon2id

# fast build rule for target.
test_argon2id/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_argon2id.dir\build.make CMakeFiles/test_argon2id.dir/build
.PHONY : test_argon2id/fast

#=============================================================================
# Target rules for targets named demo_argon2id

# Build rule for target.
demo_argon2id: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 demo_argon2id
.PHONY : demo_argon2id

# fast build rule for target.
demo_argon2id/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\demo_argon2id.dir\build.make CMakeFiles/demo_argon2id.dir/build
.PHONY : demo_argon2id/fast

demo/demo.obj: demo/demo.cpp.obj
.PHONY : demo/demo.obj

# target to build an object file
demo/demo.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\demo_argon2id.dir\build.make CMakeFiles/demo_argon2id.dir/demo/demo.cpp.obj
.PHONY : demo/demo.cpp.obj

demo/demo.i: demo/demo.cpp.i
.PHONY : demo/demo.i

# target to preprocess a source file
demo/demo.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\demo_argon2id.dir\build.make CMakeFiles/demo_argon2id.dir/demo/demo.cpp.i
.PHONY : demo/demo.cpp.i

demo/demo.s: demo/demo.cpp.s
.PHONY : demo/demo.s

# target to generate assembly for a file
demo/demo.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\demo_argon2id.dir\build.make CMakeFiles/demo_argon2id.dir/demo/demo.cpp.s
.PHONY : demo/demo.cpp.s

src/Argon2idHasher.obj: src/Argon2idHasher.cpp.obj
.PHONY : src/Argon2idHasher.obj

# target to build an object file
src/Argon2idHasher.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Argon2idHasher.dir\build.make CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj
.PHONY : src/Argon2idHasher.cpp.obj

src/Argon2idHasher.i: src/Argon2idHasher.cpp.i
.PHONY : src/Argon2idHasher.i

# target to preprocess a source file
src/Argon2idHasher.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Argon2idHasher.dir\build.make CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.i
.PHONY : src/Argon2idHasher.cpp.i

src/Argon2idHasher.s: src/Argon2idHasher.cpp.s
.PHONY : src/Argon2idHasher.s

# target to generate assembly for a file
src/Argon2idHasher.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Argon2idHasher.dir\build.make CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.s
.PHONY : src/Argon2idHasher.cpp.s

tests/test_argon2id.obj: tests/test_argon2id.cpp.obj
.PHONY : tests/test_argon2id.obj

# target to build an object file
tests/test_argon2id.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_argon2id.dir\build.make CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj
.PHONY : tests/test_argon2id.cpp.obj

tests/test_argon2id.i: tests/test_argon2id.cpp.i
.PHONY : tests/test_argon2id.i

# target to preprocess a source file
tests/test_argon2id.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_argon2id.dir\build.make CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.i
.PHONY : tests/test_argon2id.cpp.i

tests/test_argon2id.s: tests/test_argon2id.cpp.s
.PHONY : tests/test_argon2id.s

# target to generate assembly for a file
tests/test_argon2id.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_argon2id.dir\build.make CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.s
.PHONY : tests/test_argon2id.cpp.s

third_party/argon2/src/argon2.obj: third_party/argon2/src/argon2.c.obj
.PHONY : third_party/argon2/src/argon2.obj

# target to build an object file
third_party/argon2/src/argon2.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj
.PHONY : third_party/argon2/src/argon2.c.obj

third_party/argon2/src/argon2.i: third_party/argon2/src/argon2.c.i
.PHONY : third_party/argon2/src/argon2.i

# target to preprocess a source file
third_party/argon2/src/argon2.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.i
.PHONY : third_party/argon2/src/argon2.c.i

third_party/argon2/src/argon2.s: third_party/argon2/src/argon2.c.s
.PHONY : third_party/argon2/src/argon2.s

# target to generate assembly for a file
third_party/argon2/src/argon2.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.s
.PHONY : third_party/argon2/src/argon2.c.s

third_party/argon2/src/blake2/blake2b.obj: third_party/argon2/src/blake2/blake2b.c.obj
.PHONY : third_party/argon2/src/blake2/blake2b.obj

# target to build an object file
third_party/argon2/src/blake2/blake2b.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj
.PHONY : third_party/argon2/src/blake2/blake2b.c.obj

third_party/argon2/src/blake2/blake2b.i: third_party/argon2/src/blake2/blake2b.c.i
.PHONY : third_party/argon2/src/blake2/blake2b.i

# target to preprocess a source file
third_party/argon2/src/blake2/blake2b.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.i
.PHONY : third_party/argon2/src/blake2/blake2b.c.i

third_party/argon2/src/blake2/blake2b.s: third_party/argon2/src/blake2/blake2b.c.s
.PHONY : third_party/argon2/src/blake2/blake2b.s

# target to generate assembly for a file
third_party/argon2/src/blake2/blake2b.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.s
.PHONY : third_party/argon2/src/blake2/blake2b.c.s

third_party/argon2/src/core.obj: third_party/argon2/src/core.c.obj
.PHONY : third_party/argon2/src/core.obj

# target to build an object file
third_party/argon2/src/core.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj
.PHONY : third_party/argon2/src/core.c.obj

third_party/argon2/src/core.i: third_party/argon2/src/core.c.i
.PHONY : third_party/argon2/src/core.i

# target to preprocess a source file
third_party/argon2/src/core.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.i
.PHONY : third_party/argon2/src/core.c.i

third_party/argon2/src/core.s: third_party/argon2/src/core.c.s
.PHONY : third_party/argon2/src/core.s

# target to generate assembly for a file
third_party/argon2/src/core.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.s
.PHONY : third_party/argon2/src/core.c.s

third_party/argon2/src/encoding.obj: third_party/argon2/src/encoding.c.obj
.PHONY : third_party/argon2/src/encoding.obj

# target to build an object file
third_party/argon2/src/encoding.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj
.PHONY : third_party/argon2/src/encoding.c.obj

third_party/argon2/src/encoding.i: third_party/argon2/src/encoding.c.i
.PHONY : third_party/argon2/src/encoding.i

# target to preprocess a source file
third_party/argon2/src/encoding.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.i
.PHONY : third_party/argon2/src/encoding.c.i

third_party/argon2/src/encoding.s: third_party/argon2/src/encoding.c.s
.PHONY : third_party/argon2/src/encoding.s

# target to generate assembly for a file
third_party/argon2/src/encoding.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.s
.PHONY : third_party/argon2/src/encoding.c.s

third_party/argon2/src/opt.obj: third_party/argon2/src/opt.c.obj
.PHONY : third_party/argon2/src/opt.obj

# target to build an object file
third_party/argon2/src/opt.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj
.PHONY : third_party/argon2/src/opt.c.obj

third_party/argon2/src/opt.i: third_party/argon2/src/opt.c.i
.PHONY : third_party/argon2/src/opt.i

# target to preprocess a source file
third_party/argon2/src/opt.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.i
.PHONY : third_party/argon2/src/opt.c.i

third_party/argon2/src/opt.s: third_party/argon2/src/opt.c.s
.PHONY : third_party/argon2/src/opt.s

# target to generate assembly for a file
third_party/argon2/src/opt.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.s
.PHONY : third_party/argon2/src/opt.c.s

third_party/argon2/src/thread.obj: third_party/argon2/src/thread.c.obj
.PHONY : third_party/argon2/src/thread.obj

# target to build an object file
third_party/argon2/src/thread.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj
.PHONY : third_party/argon2/src/thread.c.obj

third_party/argon2/src/thread.i: third_party/argon2/src/thread.c.i
.PHONY : third_party/argon2/src/thread.i

# target to preprocess a source file
third_party/argon2/src/thread.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.i
.PHONY : third_party/argon2/src/thread.c.i

third_party/argon2/src/thread.s: third_party/argon2/src/thread.c.s
.PHONY : third_party/argon2/src/thread.s

# target to generate assembly for a file
third_party/argon2/src/thread.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.s
.PHONY : third_party/argon2/src/thread.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... test
	@echo ... Argon2idHasher
	@echo ... argon2_static
	@echo ... demo_argon2id
	@echo ... test_argon2id
	@echo ... demo/demo.obj
	@echo ... demo/demo.i
	@echo ... demo/demo.s
	@echo ... src/Argon2idHasher.obj
	@echo ... src/Argon2idHasher.i
	@echo ... src/Argon2idHasher.s
	@echo ... tests/test_argon2id.obj
	@echo ... tests/test_argon2id.i
	@echo ... tests/test_argon2id.s
	@echo ... third_party/argon2/src/argon2.obj
	@echo ... third_party/argon2/src/argon2.i
	@echo ... third_party/argon2/src/argon2.s
	@echo ... third_party/argon2/src/blake2/blake2b.obj
	@echo ... third_party/argon2/src/blake2/blake2b.i
	@echo ... third_party/argon2/src/blake2/blake2b.s
	@echo ... third_party/argon2/src/core.obj
	@echo ... third_party/argon2/src/core.i
	@echo ... third_party/argon2/src/core.s
	@echo ... third_party/argon2/src/encoding.obj
	@echo ... third_party/argon2/src/encoding.i
	@echo ... third_party/argon2/src/encoding.s
	@echo ... third_party/argon2/src/opt.obj
	@echo ... third_party/argon2/src/opt.i
	@echo ... third_party/argon2/src/opt.s
	@echo ... third_party/argon2/src/thread.obj
	@echo ... third_party/argon2/src/thread.i
	@echo ... third_party/argon2/src/thread.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

