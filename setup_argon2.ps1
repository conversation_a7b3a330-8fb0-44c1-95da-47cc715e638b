# PowerShell script to download and setup Argon2 library
# This script downloads the official Argon2 implementation from GitHub

Write-Host "Setting up Argon2 library..." -ForegroundColor Green

# Create third_party directory if it doesn't exist
$thirdPartyDir = "third_party"
if (-not (Test-Path $thirdPartyDir)) {
    New-Item -ItemType Directory -Path $thirdPartyDir
    Write-Host "Created third_party directory" -ForegroundColor Yellow
}

# Change to third_party directory
Set-Location $thirdPartyDir

# Check if argon2 directory already exists
if (Test-Path "argon2") {
    Write-Host "Argon2 directory already exists. Removing old version..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "argon2"
}

# Download Argon2 source code
$argon2Url = "https://github.com/P-H-C/phc-winner-argon2/archive/refs/tags/20190702.zip"
$zipFile = "argon2.zip"

Write-Host "Downloading Argon2 source code..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $argon2Url -OutFile $zipFile
    Write-Host "Download completed successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to download Argon2 source: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Extract the zip file
Write-Host "Extracting Argon2 source code..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $zipFile -DestinationPath . -Force
    
    # Rename the extracted directory to 'argon2'
    $extractedDir = Get-ChildItem -Directory | Where-Object { $_.Name -like "phc-winner-argon2*" } | Select-Object -First 1
    if ($extractedDir) {
        Rename-Item -Path $extractedDir.FullName -NewName "argon2"
        Write-Host "Extraction completed successfully" -ForegroundColor Green
    } else {
        throw "Could not find extracted directory"
    }
} catch {
    Write-Host "Failed to extract Argon2 source: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# Clean up zip file
Remove-Item $zipFile -Force

# Verify the structure
if (Test-Path "argon2/src/argon2.c" -and Test-Path "argon2/include/argon2.h") {
    Write-Host "Argon2 library setup completed successfully!" -ForegroundColor Green
    Write-Host "Found required files:" -ForegroundColor Cyan
    Write-Host "  - argon2/src/argon2.c" -ForegroundColor Cyan
    Write-Host "  - argon2/include/argon2.h" -ForegroundColor Cyan
} else {
    Write-Host "Warning: Some required Argon2 files may be missing" -ForegroundColor Red
}

# Return to project root
Set-Location ..

Write-Host ""
Write-Host "Setup complete! You can now build the project using:" -ForegroundColor Green
Write-Host "  mkdir build" -ForegroundColor Cyan
Write-Host "  cd build" -ForegroundColor Cyan
Write-Host "  cmake .. -G 'MSYS Makefiles'" -ForegroundColor Cyan
Write-Host "  make" -ForegroundColor Cyan
