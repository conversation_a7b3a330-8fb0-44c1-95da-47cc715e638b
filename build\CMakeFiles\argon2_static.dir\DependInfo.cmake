
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/argon2.c" "CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj" "gcc" "CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj.d"
  "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/blake2/blake2b.c" "CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj" "gcc" "CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj.d"
  "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/core.c" "CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj" "gcc" "CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj.d"
  "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/encoding.c" "CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj" "gcc" "CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj.d"
  "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/opt.c" "CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj" "gcc" "CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj.d"
  "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/thread.c" "CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj" "gcc" "CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
