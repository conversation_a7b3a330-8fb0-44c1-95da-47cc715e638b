{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 104, "parent": 0}, {"command": 0, "file": 0, "line": 110, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["libArgon2idHasher.a"], "targetId": "Argon2idHasher::@6890427a1f51a3e7e1df", "targetIndex": 0, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["libargon2_static.a"], "targetId": "argon2_static::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 2, "component": "Unspecified", "destination": "include", "paths": ["include/Argon2idHasher.h"], "type": "file"}], "paths": {"build": ".", "source": "."}}