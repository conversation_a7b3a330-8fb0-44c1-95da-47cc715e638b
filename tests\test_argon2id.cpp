#include "Argon2idHasher.h"
#include <iostream>
#include <cassert>
#include <chrono>
#include <thread>
#include <vector>
#include <functional>

// Simple test framework
class TestRunner {
private:
    int total_tests = 0;
    int passed_tests = 0;

public:
    template<typename TestFunc>
    void run_test(const std::string& test_name, TestFunc test_func) {
        total_tests++;
        std::cout << "Running test: " << test_name << "... ";

        try {
            test_func();
            passed_tests++;
            std::cout << "PASSED" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "FAILED - " << e.what() << std::endl;
        } catch (...) {
            std::cout << "FAILED - Unknown exception" << std::endl;
        }
    }
    
    void print_summary() {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Total tests: " << total_tests << std::endl;
        std::cout << "Passed: " << passed_tests << std::endl;
        std::cout << "Failed: " << (total_tests - passed_tests) << std::endl;
        
        if (passed_tests == total_tests) {
            std::cout << "All tests PASSED!" << std::endl;
        } else {
            std::cout << "Some tests FAILED!" << std::endl;
        }
    }
    
    bool all_passed() const {
        return passed_tests == total_tests;
    }
};

// Test helper function
void assert_true(bool condition, const std::string& message) {
    if (!condition) {
        throw std::runtime_error(message);
    }
}

void assert_false(bool condition, const std::string& message) {
    if (condition) {
        throw std::runtime_error(message);
    }
}

// Test functions
void test_library_availability() {
    assert_true(Argon2idHasher::isAvailable(), "Argon2 library should be available");
}

void test_default_constructor() {
    Argon2idHasher hasher;
    auto config = hasher.getConfig();
    
    assert_true(config.memory_cost == 65536, "Default memory cost should be 65536");
    assert_true(config.time_cost == 3, "Default time cost should be 3");
    assert_true(config.parallelism == 4, "Default parallelism should be 4");
    assert_true(config.salt_length == 16, "Default salt length should be 16");
    assert_true(config.hash_length == 32, "Default hash length should be 32");
}

void test_custom_constructor() {
    Argon2Config config(1024, 2, 2, 16, 32);
    Argon2idHasher hasher(config);
    
    auto retrieved_config = hasher.getConfig();
    assert_true(retrieved_config.memory_cost == 1024, "Custom memory cost should be 1024");
    assert_true(retrieved_config.time_cost == 2, "Custom time cost should be 2");
    assert_true(retrieved_config.parallelism == 2, "Custom parallelism should be 2");
}

void test_invalid_config() {
    try {
        Argon2Config invalid_config(4, 0, 1, 16, 32);  // time_cost = 0 is invalid
        assert_true(false, "Should have thrown exception for invalid config");
    } catch (const Argon2Exception&) {
        // Expected
    }
}

void test_password_hashing() {
    Argon2idHasher hasher;
    std::string password = "test_password_123";
    
    std::string hash = hasher.hash(password);
    
    assert_true(!hash.empty(), "Hash should not be empty");
    assert_true(hash.find("$argon2id$") == 0, "Hash should start with $argon2id$");
    assert_true(hash.length() > 50, "Hash should be reasonably long");
}

void test_password_verification() {
    Argon2idHasher hasher;
    std::string password = "test_password_456";
    
    std::string hash = hasher.hash(password);
    
    // Test correct password
    assert_true(hasher.verify(password, hash), "Correct password should verify");
    
    // Test incorrect password
    assert_false(hasher.verify("wrong_password", hash), "Wrong password should not verify");
    
    // Test empty password
    assert_false(hasher.verify("", hash), "Empty password should not verify");
    
    // Test empty hash
    assert_false(hasher.verify(password, ""), "Empty hash should not verify");
}

void test_different_passwords_different_hashes() {
    Argon2idHasher hasher;
    
    std::string hash1 = hasher.hash("password1");
    std::string hash2 = hasher.hash("password2");
    std::string hash3 = hasher.hash("password1");  // Same password as hash1
    
    assert_true(hash1 != hash2, "Different passwords should produce different hashes");
    assert_true(hash1 != hash3, "Same password should produce different hashes (due to salt)");
}

void test_salt_generation() {
    auto salt1 = Argon2idHasher::generateSalt(16);
    auto salt2 = Argon2idHasher::generateSalt(16);
    
    assert_true(salt1.size() == 16, "Salt should be 16 bytes");
    assert_true(salt2.size() == 16, "Salt should be 16 bytes");
    assert_true(salt1 != salt2, "Different salt generations should produce different results");
}

void test_config_update() {
    Argon2idHasher hasher;
    
    Argon2Config new_config(2048, 2, 2, 16, 32);
    hasher.setConfig(new_config);
    
    auto retrieved_config = hasher.getConfig();
    assert_true(retrieved_config.memory_cost == 2048, "Updated memory cost should be 2048");
    assert_true(retrieved_config.time_cost == 2, "Updated time cost should be 2");
}

void test_move_semantics() {
    Argon2Config config(1024, 2, 2, 16, 32);
    Argon2idHasher hasher1(config);
    
    // Test move constructor
    Argon2idHasher hasher2 = std::move(hasher1);
    auto config2 = hasher2.getConfig();
    assert_true(config2.memory_cost == 1024, "Moved hasher should retain configuration");
    
    // Test move assignment
    Argon2idHasher hasher3;
    hasher3 = std::move(hasher2);
    auto config3 = hasher3.getConfig();
    assert_true(config3.memory_cost == 1024, "Move-assigned hasher should retain configuration");
}

void test_thread_safety() {
    Argon2idHasher hasher;
    std::vector<std::thread> threads;
    std::vector<std::string> results(4);
    
    // Test concurrent hashing
    for (int i = 0; i < 4; ++i) {
        threads.emplace_back([&hasher, &results, i]() {
            results[i] = hasher.hash("password" + std::to_string(i));
        });
    }
    
    for (auto& t : threads) {
        t.join();
    }
    
    // Verify all hashes were generated
    for (const auto& result : results) {
        assert_true(!result.empty(), "All concurrent hashes should be generated");
        assert_true(result.find("$argon2id$") == 0, "All hashes should be valid Argon2id format");
    }
}

void test_performance_benchmark() {
    std::cout << "\n=== Performance Benchmark ===" << std::endl;
    
    Argon2Config fast_config(1024, 1, 1, 16, 32);  // Fast config for testing
    Argon2idHasher hasher(fast_config);
    
    const int num_iterations = 5;
    std::string password = "benchmark_password";
    
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_iterations; ++i) {
        std::string hash = hasher.hash(password + std::to_string(i));
        assert_true(hasher.verify(password + std::to_string(i), hash), 
                   "Benchmark verification should succeed");
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "Completed " << num_iterations << " hash+verify cycles in " 
              << duration.count() << "ms" << std::endl;
    std::cout << "Average time per cycle: " << (duration.count() / num_iterations) 
              << "ms" << std::endl;
}

int main() {
    std::cout << "=== Argon2id Hasher Test Suite ===" << std::endl;
    
    TestRunner runner;
    
    // Run all tests
    runner.run_test("Library Availability", test_library_availability);
    runner.run_test("Default Constructor", test_default_constructor);
    runner.run_test("Custom Constructor", test_custom_constructor);
    runner.run_test("Invalid Config", test_invalid_config);
    runner.run_test("Password Hashing", test_password_hashing);
    runner.run_test("Password Verification", test_password_verification);
    runner.run_test("Different Passwords Different Hashes", test_different_passwords_different_hashes);
    runner.run_test("Salt Generation", test_salt_generation);
    runner.run_test("Config Update", test_config_update);
    runner.run_test("Move Semantics", test_move_semantics);
    runner.run_test("Thread Safety", test_thread_safety);
    
    // Performance benchmark (not counted in pass/fail)
    try {
        test_performance_benchmark();
    } catch (const std::exception& e) {
        std::cout << "Performance benchmark failed: " << e.what() << std::endl;
    }
    
    runner.print_summary();
    
    return runner.all_passed() ? 0 : 1;
}
