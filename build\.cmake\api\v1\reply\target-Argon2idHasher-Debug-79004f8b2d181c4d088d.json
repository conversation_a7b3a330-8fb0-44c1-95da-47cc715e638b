{"archive": {}, "artifacts": [{"path": "libArgon2idHasher.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "add_compile_options", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 64, "parent": 0}, {"command": 1, "file": 0, "line": 104, "parent": 0}, {"command": 2, "file": 0, "line": 72, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}, {"command": 3, "file": 0, "line": 21, "parent": 0}, {"command": 4, "file": 0, "line": 31, "parent": 0}, {"command": 4, "file": 0, "line": 32, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=c++17"}, {"backtrace": 4, "fragment": "-Wall"}, {"backtrace": 4, "fragment": "-Wextra"}, {"backtrace": 4, "fragment": "-Wpedantic"}, {"backtrace": 4, "fragment": "-Werror"}, {"backtrace": 5, "fragment": "-g"}, {"backtrace": 5, "fragment": "-O0"}], "includes": [{"backtrace": 6, "path": "E:/workspace/vscode/Argon2idDemo/include"}, {"backtrace": 7, "path": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/include"}, {"backtrace": 3, "path": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "argon2_static::@6890427a1f51a3e7e1df"}], "id": "Argon2idHasher::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/Argon2idDemo"}}, "name": "Argon2idHasher", "nameOnDisk": "libArgon2idHasher.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/Argon2idHasher.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}