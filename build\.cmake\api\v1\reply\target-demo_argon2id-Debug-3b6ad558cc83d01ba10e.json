{"artifacts": [{"path": "demo_argon2id.exe"}, {"path": "demo_argon2id.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "add_compile_options", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 89, "parent": 0}, {"command": 1, "file": 0, "line": 93, "parent": 0}, {"command": 2, "file": 0, "line": 19, "parent": 0}, {"command": 2, "file": 0, "line": 21, "parent": 0}, {"command": 3, "file": 0, "line": 31, "parent": 0}, {"command": 3, "file": 0, "line": 32, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=c++17"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-Wpedantic"}, {"backtrace": 3, "fragment": "-Werror"}, {"backtrace": 4, "fragment": "-g"}, {"backtrace": 4, "fragment": "-O0"}], "includes": [{"backtrace": 5, "path": "E:/workspace/vscode/Argon2idDemo/include"}, {"backtrace": 6, "path": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/include"}, {"backtrace": 2, "path": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "argon2_static::@6890427a1f51a3e7e1df"}, {"backtrace": 2, "id": "Argon2idHasher::@6890427a1f51a3e7e1df"}], "id": "demo_argon2id::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "libArgon2idHasher.a", "role": "libraries"}, {"backtrace": 2, "fragment": "libargon2_static.a", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "demo_argon2id", "nameOnDisk": "demo_argon2id.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "demo/demo.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}