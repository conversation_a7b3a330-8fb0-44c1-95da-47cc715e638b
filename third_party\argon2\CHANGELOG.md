# 20171227
* Added ABI version number
* AVX2/AVX-512F optimizations of BLAMKA
* Set Argon2 version number from the command line
* New bindings
* Minor bug and warning fixes (no security issue)

# 20161029

* Argon2id added
* Better documentation
* Dual licensing CC0 / Apache 2.0
* Minor bug fixes (no security issue)

# 20160406

* Version 1.3 of Argon2
* Version number in encoded hash
* Refactored low-level API
* Visibility control for library symbols
* Microsoft Visual Studio solution
* New bindings
* Minor bug and warning fixes (no security issue)


# 20151206

* Python bindings
* Password read from stdin, instead of being an argument
* Compatibility FreeBSD, NetBSD, OpenBSD
* Constant-time verification
* Minor bug and warning fixes (no security issue)
