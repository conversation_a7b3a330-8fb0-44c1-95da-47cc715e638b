#ifndef ARGON2ID_HASHER_H
#define ARGON2ID_HASHER_H

#include <string>
#include <vector>
#include <memory>
#include <stdexcept>
#include <random>
#include <mutex>
#include <cstdint>

/**
 * @brief Exception class for Argon2id hashing errors
 */
class Argon2Exception : public std::runtime_error {
public:
    explicit Argon2Exception(const std::string& message) 
        : std::runtime_error("Argon2 Error: " + message) {}
};

/**
 * @brief Configuration parameters for Argon2id hashing
 */
struct Argon2Config {
    uint32_t memory_cost;      // Memory usage in KiB (default: 65536 = 64MB)
    uint32_t time_cost;        // Number of iterations (default: 3)
    uint32_t parallelism;      // Number of parallel threads (default: 4)
    uint32_t salt_length;      // Salt length in bytes (default: 16)
    uint32_t hash_length;      // Hash output length in bytes (default: 32)
    
    // Default constructor with recommended secure parameters
    Argon2Config() 
        : memory_cost(65536)    // 64MB
        , time_cost(3)          // 3 iterations
        , parallelism(4)        // 4 threads
        , salt_length(16)       // 16 bytes salt
        , hash_length(32)       // 32 bytes hash (256 bits)
    {}
    
    // Custom constructor
    Argon2Config(uint32_t mem, uint32_t time, uint32_t parallel, 
                 uint32_t salt_len, uint32_t hash_len)
        : memory_cost(mem)
        , time_cost(time)
        , parallelism(parallel)
        , salt_length(salt_len)
        , hash_length(hash_len)
    {
        validate();
    }
    
    // Validate configuration parameters
    void validate() const {
        if (memory_cost < 8) {
            throw Argon2Exception("Memory cost must be at least 8 KiB");
        }
        if (time_cost < 1) {
            throw Argon2Exception("Time cost must be at least 1");
        }
        if (parallelism < 1 || parallelism > 16777215) {
            throw Argon2Exception("Parallelism must be between 1 and 16777215");
        }
        if (salt_length < 8 || salt_length > 4294967295U) {
            throw Argon2Exception("Salt length must be between 8 and 4294967295 bytes");
        }
        if (hash_length < 4 || hash_length > 4294967295U) {
            throw Argon2Exception("Hash length must be between 4 and 4294967295 bytes");
        }
    }
};

/**
 * @brief Argon2id password hashing class
 * 
 * This class provides a secure C++ wrapper around the Argon2id password hashing algorithm.
 * It supports configurable parameters and provides both hashing and verification functionality.
 * 
 * Features:
 * - Automatic salt generation using cryptographically secure random number generator
 * - Configurable memory cost, time cost, and parallelism
 * - Standard Argon2id encoded string format output
 * - Thread-safe operations
 * - Memory-safe implementation with automatic cleanup
 * 
 * Example usage:
 * @code
 * Argon2idHasher hasher;
 * std::string hash = hasher.hash("mypassword");
 * bool valid = hasher.verify("mypassword", hash);
 * @endcode
 */
class Argon2idHasher {
public:
    /**
     * @brief Default constructor with secure default parameters
     */
    Argon2idHasher();
    
    /**
     * @brief Constructor with custom configuration
     * @param config Argon2 configuration parameters
     */
    explicit Argon2idHasher(const Argon2Config& config);
    
    /**
     * @brief Destructor - ensures secure memory cleanup
     */
    ~Argon2idHasher();
    
    // Disable copy constructor and assignment operator for security
    Argon2idHasher(const Argon2idHasher&) = delete;
    Argon2idHasher& operator=(const Argon2idHasher&) = delete;
    
    // Enable move constructor and assignment operator
    Argon2idHasher(Argon2idHasher&& other) noexcept;
    Argon2idHasher& operator=(Argon2idHasher&& other) noexcept;
    
    /**
     * @brief Hash a password using Argon2id
     * @param password The password to hash
     * @return Encoded Argon2id hash string in PHC format
     * @throws Argon2Exception on hashing failure
     */
    std::string hash(const std::string& password);
    
    /**
     * @brief Verify a password against an Argon2id hash
     * @param password The password to verify
     * @param encoded_hash The encoded hash string to verify against
     * @return true if password matches, false otherwise
     * @throws Argon2Exception on verification error (not password mismatch)
     */
    bool verify(const std::string& password, const std::string& encoded_hash);
    
    /**
     * @brief Get current configuration
     * @return Copy of current Argon2Config
     */
    Argon2Config getConfig() const;
    
    /**
     * @brief Update configuration (thread-safe)
     * @param config New configuration parameters
     * @throws Argon2Exception if configuration is invalid
     */
    void setConfig(const Argon2Config& config);
    
    /**
     * @brief Generate a random salt
     * @param length Salt length in bytes
     * @return Random salt as byte vector
     */
    static std::vector<uint8_t> generateSalt(size_t length);
    
    /**
     * @brief Check if Argon2 library is available and working
     * @return true if library is functional
     */
    static bool isAvailable();

private:
    Argon2Config config_;
    mutable std::mutex config_mutex_;  // For thread-safe configuration updates
    
    // Internal helper methods
    std::vector<uint8_t> generateRandomSalt() const;
    void secureZeroMemory(void* ptr, size_t size) const;
    std::string encodeHash(const std::vector<uint8_t>& salt, 
                          const std::vector<uint8_t>& hash) const;
};

#endif // ARGON2ID_HASHER_H
