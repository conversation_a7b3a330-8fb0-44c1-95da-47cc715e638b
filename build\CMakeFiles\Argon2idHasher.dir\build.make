# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\Argon2idDemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\Argon2idDemo\build

# Include any dependencies generated for this target.
include CMakeFiles/Argon2idHasher.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/Argon2idHasher.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/Argon2idHasher.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/Argon2idHasher.dir/flags.make

CMakeFiles/Argon2idHasher.dir/codegen:
.PHONY : CMakeFiles/Argon2idHasher.dir/codegen

CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj: CMakeFiles/Argon2idHasher.dir/flags.make
CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj: CMakeFiles/Argon2idHasher.dir/includes_CXX.rsp
CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj: E:/workspace/vscode/Argon2idDemo/src/Argon2idHasher.cpp
CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj: CMakeFiles/Argon2idHasher.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj -MF CMakeFiles\Argon2idHasher.dir\src\Argon2idHasher.cpp.obj.d -o CMakeFiles\Argon2idHasher.dir\src\Argon2idHasher.cpp.obj -c E:\workspace\vscode\Argon2idDemo\src\Argon2idHasher.cpp

CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\Argon2idDemo\src\Argon2idHasher.cpp > CMakeFiles\Argon2idHasher.dir\src\Argon2idHasher.cpp.i

CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\Argon2idDemo\src\Argon2idHasher.cpp -o CMakeFiles\Argon2idHasher.dir\src\Argon2idHasher.cpp.s

# Object files for target Argon2idHasher
Argon2idHasher_OBJECTS = \
"CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj"

# External object files for target Argon2idHasher
Argon2idHasher_EXTERNAL_OBJECTS =

libArgon2idHasher.a: CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj
libArgon2idHasher.a: CMakeFiles/Argon2idHasher.dir/build.make
libArgon2idHasher.a: CMakeFiles/Argon2idHasher.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libArgon2idHasher.a"
	$(CMAKE_COMMAND) -P CMakeFiles\Argon2idHasher.dir\cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\Argon2idHasher.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/Argon2idHasher.dir/build: libArgon2idHasher.a
.PHONY : CMakeFiles/Argon2idHasher.dir/build

CMakeFiles/Argon2idHasher.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\Argon2idHasher.dir\cmake_clean.cmake
.PHONY : CMakeFiles/Argon2idHasher.dir/clean

CMakeFiles/Argon2idHasher.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\workspace\vscode\Argon2idDemo E:\workspace\vscode\Argon2idDemo E:\workspace\vscode\Argon2idDemo\build E:\workspace\vscode\Argon2idDemo\build E:\workspace\vscode\Argon2idDemo\build\CMakeFiles\Argon2idHasher.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/Argon2idHasher.dir/depend

