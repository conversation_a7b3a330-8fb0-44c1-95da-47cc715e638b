# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\Argon2idDemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\Argon2idDemo\build

# Include any dependencies generated for this target.
include CMakeFiles/test_argon2id.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_argon2id.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_argon2id.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_argon2id.dir/flags.make

CMakeFiles/test_argon2id.dir/codegen:
.PHONY : CMakeFiles/test_argon2id.dir/codegen

CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj: CMakeFiles/test_argon2id.dir/flags.make
CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj: CMakeFiles/test_argon2id.dir/includes_CXX.rsp
CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj: E:/workspace/vscode/Argon2idDemo/tests/test_argon2id.cpp
CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj: CMakeFiles/test_argon2id.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj -MF CMakeFiles\test_argon2id.dir\tests\test_argon2id.cpp.obj.d -o CMakeFiles\test_argon2id.dir\tests\test_argon2id.cpp.obj -c E:\workspace\vscode\Argon2idDemo\tests\test_argon2id.cpp

CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.i"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E E:\workspace\vscode\Argon2idDemo\tests\test_argon2id.cpp > CMakeFiles\test_argon2id.dir\tests\test_argon2id.cpp.i

CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.s"
	D:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S E:\workspace\vscode\Argon2idDemo\tests\test_argon2id.cpp -o CMakeFiles\test_argon2id.dir\tests\test_argon2id.cpp.s

# Object files for target test_argon2id
test_argon2id_OBJECTS = \
"CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj"

# External object files for target test_argon2id
test_argon2id_EXTERNAL_OBJECTS =

test_argon2id.exe: CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj
test_argon2id.exe: CMakeFiles/test_argon2id.dir/build.make
test_argon2id.exe: libArgon2idHasher.a
test_argon2id.exe: libargon2_static.a
test_argon2id.exe: CMakeFiles/test_argon2id.dir/linkLibs.rsp
test_argon2id.exe: CMakeFiles/test_argon2id.dir/objects1.rsp
test_argon2id.exe: CMakeFiles/test_argon2id.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable test_argon2id.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\test_argon2id.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_argon2id.dir/build: test_argon2id.exe
.PHONY : CMakeFiles/test_argon2id.dir/build

CMakeFiles/test_argon2id.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\test_argon2id.dir\cmake_clean.cmake
.PHONY : CMakeFiles/test_argon2id.dir/clean

CMakeFiles/test_argon2id.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\workspace\vscode\Argon2idDemo E:\workspace\vscode\Argon2idDemo E:\workspace\vscode\Argon2idDemo\build E:\workspace\vscode\Argon2idDemo\build E:\workspace\vscode\Argon2idDemo\build\CMakeFiles\test_argon2id.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_argon2id.dir/depend

