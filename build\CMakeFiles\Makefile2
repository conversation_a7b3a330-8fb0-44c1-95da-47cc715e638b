# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\Argon2idDemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\Argon2idDemo\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/argon2_static.dir/all
all: CMakeFiles/Argon2idHasher.dir/all
all: CMakeFiles/test_argon2id.dir/all
all: CMakeFiles/demo_argon2id.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/argon2_static.dir/codegen
codegen: CMakeFiles/Argon2idHasher.dir/codegen
codegen: CMakeFiles/test_argon2id.dir/codegen
codegen: CMakeFiles/demo_argon2id.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/argon2_static.dir/clean
clean: CMakeFiles/Argon2idHasher.dir/clean
clean: CMakeFiles/test_argon2id.dir/clean
clean: CMakeFiles/demo_argon2id.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/argon2_static.dir

# All Build rule for target.
CMakeFiles/argon2_static.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=3,4,5,6,7,8,9 "Built target argon2_static"
.PHONY : CMakeFiles/argon2_static.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/argon2_static.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/argon2_static.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 0
.PHONY : CMakeFiles/argon2_static.dir/rule

# Convenience name for target.
argon2_static: CMakeFiles/argon2_static.dir/rule
.PHONY : argon2_static

# codegen rule for target.
CMakeFiles/argon2_static.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=3,4,5,6,7,8,9 "Finished codegen for target argon2_static"
.PHONY : CMakeFiles/argon2_static.dir/codegen

# clean rule for target.
CMakeFiles/argon2_static.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\argon2_static.dir\build.make CMakeFiles/argon2_static.dir/clean
.PHONY : CMakeFiles/argon2_static.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/Argon2idHasher.dir

# All Build rule for target.
CMakeFiles/Argon2idHasher.dir/all: CMakeFiles/argon2_static.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Argon2idHasher.dir\build.make CMakeFiles/Argon2idHasher.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Argon2idHasher.dir\build.make CMakeFiles/Argon2idHasher.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=1,2 "Built target Argon2idHasher"
.PHONY : CMakeFiles/Argon2idHasher.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/Argon2idHasher.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/Argon2idHasher.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 0
.PHONY : CMakeFiles/Argon2idHasher.dir/rule

# Convenience name for target.
Argon2idHasher: CMakeFiles/Argon2idHasher.dir/rule
.PHONY : Argon2idHasher

# codegen rule for target.
CMakeFiles/Argon2idHasher.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Argon2idHasher.dir\build.make CMakeFiles/Argon2idHasher.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=1,2 "Finished codegen for target Argon2idHasher"
.PHONY : CMakeFiles/Argon2idHasher.dir/codegen

# clean rule for target.
CMakeFiles/Argon2idHasher.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Argon2idHasher.dir\build.make CMakeFiles/Argon2idHasher.dir/clean
.PHONY : CMakeFiles/Argon2idHasher.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_argon2id.dir

# All Build rule for target.
CMakeFiles/test_argon2id.dir/all: CMakeFiles/argon2_static.dir/all
CMakeFiles/test_argon2id.dir/all: CMakeFiles/Argon2idHasher.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_argon2id.dir\build.make CMakeFiles/test_argon2id.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_argon2id.dir\build.make CMakeFiles/test_argon2id.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=12,13 "Built target test_argon2id"
.PHONY : CMakeFiles/test_argon2id.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_argon2id.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/test_argon2id.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 0
.PHONY : CMakeFiles/test_argon2id.dir/rule

# Convenience name for target.
test_argon2id: CMakeFiles/test_argon2id.dir/rule
.PHONY : test_argon2id

# codegen rule for target.
CMakeFiles/test_argon2id.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_argon2id.dir\build.make CMakeFiles/test_argon2id.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=12,13 "Finished codegen for target test_argon2id"
.PHONY : CMakeFiles/test_argon2id.dir/codegen

# clean rule for target.
CMakeFiles/test_argon2id.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\test_argon2id.dir\build.make CMakeFiles/test_argon2id.dir/clean
.PHONY : CMakeFiles/test_argon2id.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/demo_argon2id.dir

# All Build rule for target.
CMakeFiles/demo_argon2id.dir/all: CMakeFiles/argon2_static.dir/all
CMakeFiles/demo_argon2id.dir/all: CMakeFiles/Argon2idHasher.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\demo_argon2id.dir\build.make CMakeFiles/demo_argon2id.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\demo_argon2id.dir\build.make CMakeFiles/demo_argon2id.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=10,11 "Built target demo_argon2id"
.PHONY : CMakeFiles/demo_argon2id.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/demo_argon2id.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 11
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/demo_argon2id.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\workspace\vscode\Argon2idDemo\build\CMakeFiles 0
.PHONY : CMakeFiles/demo_argon2id.dir/rule

# Convenience name for target.
demo_argon2id: CMakeFiles/demo_argon2id.dir/rule
.PHONY : demo_argon2id

# codegen rule for target.
CMakeFiles/demo_argon2id.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\demo_argon2id.dir\build.make CMakeFiles/demo_argon2id.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=10,11 "Finished codegen for target demo_argon2id"
.PHONY : CMakeFiles/demo_argon2id.dir/codegen

# clean rule for target.
CMakeFiles/demo_argon2id.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\demo_argon2id.dir\build.make CMakeFiles/demo_argon2id.dir/clean
.PHONY : CMakeFiles/demo_argon2id.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

