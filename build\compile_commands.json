[{"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\gcc.exe -DARGON2_NO_THREADS=0 @CMakeFiles/argon2_static.dir/includes_C.rsp -g -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\argon2_static.dir\\third_party\\argon2\\src\\argon2.c.obj -c E:\\workspace\\vscode\\Argon2idDemo\\third_party\\argon2\\src\\argon2.c", "file": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/argon2.c", "output": "CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj"}, {"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\gcc.exe -DARGON2_NO_THREADS=0 @CMakeFiles/argon2_static.dir/includes_C.rsp -g -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\argon2_static.dir\\third_party\\argon2\\src\\core.c.obj -c E:\\workspace\\vscode\\Argon2idDemo\\third_party\\argon2\\src\\core.c", "file": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/core.c", "output": "CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj"}, {"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\gcc.exe -DARGON2_NO_THREADS=0 @CMakeFiles/argon2_static.dir/includes_C.rsp -g -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\argon2_static.dir\\third_party\\argon2\\src\\blake2\\blake2b.c.obj -c E:\\workspace\\vscode\\Argon2idDemo\\third_party\\argon2\\src\\blake2\\blake2b.c", "file": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/blake2/blake2b.c", "output": "CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj"}, {"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\gcc.exe -DARGON2_NO_THREADS=0 @CMakeFiles/argon2_static.dir/includes_C.rsp -g -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\argon2_static.dir\\third_party\\argon2\\src\\thread.c.obj -c E:\\workspace\\vscode\\Argon2idDemo\\third_party\\argon2\\src\\thread.c", "file": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/thread.c", "output": "CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj"}, {"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\gcc.exe -DARGON2_NO_THREADS=0 @CMakeFiles/argon2_static.dir/includes_C.rsp -g -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\argon2_static.dir\\third_party\\argon2\\src\\encoding.c.obj -c E:\\workspace\\vscode\\Argon2idDemo\\third_party\\argon2\\src\\encoding.c", "file": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/encoding.c", "output": "CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj"}, {"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\gcc.exe -DARGON2_NO_THREADS=0 @CMakeFiles/argon2_static.dir/includes_C.rsp -g -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\argon2_static.dir\\third_party\\argon2\\src\\opt.c.obj -c E:\\workspace\\vscode\\Argon2idDemo\\third_party\\argon2\\src\\opt.c", "file": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/opt.c", "output": "CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj"}, {"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/Argon2idHasher.dir/includes_CXX.rsp -g -std=c++17 -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\Argon2idHasher.dir\\src\\Argon2idHasher.cpp.obj -c E:\\workspace\\vscode\\Argon2idDemo\\src\\Argon2idHasher.cpp", "file": "E:/workspace/vscode/Argon2idDemo/src/Argon2idHasher.cpp", "output": "CMakeFiles/Argon2idHasher.dir/src/Argon2idHasher.cpp.obj"}, {"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/test_argon2id.dir/includes_CXX.rsp -g -std=c++17 -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\test_argon2id.dir\\tests\\test_argon2id.cpp.obj -c E:\\workspace\\vscode\\Argon2idDemo\\tests\\test_argon2id.cpp", "file": "E:/workspace/vscode/Argon2idDemo/tests/test_argon2id.cpp", "output": "CMakeFiles/test_argon2id.dir/tests/test_argon2id.cpp.obj"}, {"directory": "E:/workspace/vscode/Argon2idDemo/build", "command": "D:\\msys64\\mingw64\\bin\\g++.exe  @CMakeFiles/demo_argon2id.dir/includes_CXX.rsp -g -std=c++17 -Wall -Wextra -Wpedantic -Werror -g -O0 -o CMakeFiles\\demo_argon2id.dir\\demo\\demo.cpp.obj -c E:\\workspace\\vscode\\Argon2idDemo\\demo\\demo.cpp", "file": "E:/workspace/vscode/Argon2idDemo/demo/demo.cpp", "output": "CMakeFiles/demo_argon2id.dir/demo/demo.cpp.obj"}]