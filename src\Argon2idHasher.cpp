#include "Argon2idHasher.h"
#include <argon2.h>
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <chrono>

// Constructor with default configuration
Argon2idHasher::Argon2idHasher() : config_() {
    config_.validate();
}

// Constructor with custom configuration
Argon2idHasher::Argon2idHasher(const Argon2Config& config) : config_(config) {
    config_.validate();
}

// Destructor
Argon2idHasher::~Argon2idHasher() {
    // Secure cleanup is handled by RAII
}

// Move constructor
Argon2idHasher::Argon2idHasher(Argon2idHasher&& other) noexcept {
    std::lock_guard<std::mutex> lock(other.config_mutex_);
    config_ = std::move(other.config_);
}

// Move assignment operator
Argon2idHasher& Argon2idHasher::operator=(Argon2idHasher&& other) noexcept {
    if (this != &other) {
        std::lock(config_mutex_, other.config_mutex_);
        std::lock_guard<std::mutex> lock1(config_mutex_, std::adopt_lock);
        std::lock_guard<std::mutex> lock2(other.config_mutex_, std::adopt_lock);
        config_ = std::move(other.config_);
    }
    return *this;
}

// Hash a password
std::string Argon2idHasher::hash(const std::string& password) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (password.empty()) {
        throw Argon2Exception("Password cannot be empty");
    }
    
    // Generate random salt
    auto salt = generateRandomSalt();
    
    // Prepare output buffer
    std::vector<uint8_t> hash_output(config_.hash_length);
    
    // Call Argon2id
    int result = argon2id_hash_raw(
        config_.time_cost,
        config_.memory_cost,
        config_.parallelism,
        password.c_str(),
        password.length(),
        salt.data(),
        salt.size(),
        hash_output.data(),
        hash_output.size()
    );
    
    if (result != ARGON2_OK) {
        // Secure cleanup
        secureZeroMemory(hash_output.data(), hash_output.size());
        throw Argon2Exception("Hashing failed: " + std::string(argon2_error_message(result)));
    }
    
    // Encode the result
    std::string encoded = encodeHash(salt, hash_output);
    
    // Secure cleanup
    secureZeroMemory(hash_output.data(), hash_output.size());
    secureZeroMemory(salt.data(), salt.size());
    
    return encoded;
}

// Verify a password
bool Argon2idHasher::verify(const std::string& password, const std::string& encoded_hash) {
    if (password.empty() || encoded_hash.empty()) {
        return false;
    }
    
    // Use Argon2's built-in verification
    int result = argon2id_verify(encoded_hash.c_str(), password.c_str(), password.length());
    
    if (result == ARGON2_OK) {
        return true;
    } else if (result == ARGON2_VERIFY_MISMATCH) {
        return false;
    } else {
        throw Argon2Exception("Verification failed: " + std::string(argon2_error_message(result)));
    }
}

// Get current configuration
Argon2Config Argon2idHasher::getConfig() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return config_;
}

// Set new configuration
void Argon2idHasher::setConfig(const Argon2Config& config) {
    config.validate();
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_ = config;
}

// Generate random salt (static method)
std::vector<uint8_t> Argon2idHasher::generateSalt(size_t length) {
    if (length == 0) {
        throw Argon2Exception("Salt length cannot be zero");
    }
    
    std::vector<uint8_t> salt(length);
    
    // Use high-quality random number generator
    std::random_device rd;
    std::mt19937_64 gen(rd());
    
    // Add additional entropy from high-resolution clock
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    auto nanos = std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();
    gen.seed(gen() ^ static_cast<uint64_t>(nanos));
    
    std::uniform_int_distribution<uint8_t> dis(0, 255);
    
    for (size_t i = 0; i < length; ++i) {
        salt[i] = dis(gen);
    }
    
    return salt;
}

// Check if Argon2 library is available
bool Argon2idHasher::isAvailable() {
    try {
        // Try a simple hash operation to verify library functionality
        const char* test_password = "test";
        const char* test_salt = "testsalt12345678";  // 16 bytes
        uint8_t test_hash[32];
        
        int result = argon2id_hash_raw(
            2,      // time_cost
            1024,   // memory_cost (1MB)
            1,      // parallelism
            test_password, strlen(test_password),
            test_salt, 16,
            test_hash, 32
        );
        
        // Secure cleanup
        memset(test_hash, 0, sizeof(test_hash));
        
        return result == ARGON2_OK;
    } catch (...) {
        return false;
    }
}

// Generate random salt (private method)
std::vector<uint8_t> Argon2idHasher::generateRandomSalt() const {
    return generateSalt(config_.salt_length);
}

// Secure memory zeroing
void Argon2idHasher::secureZeroMemory(void* ptr, size_t size) const {
    if (ptr && size > 0) {
        volatile uint8_t* volatile_ptr = static_cast<volatile uint8_t*>(ptr);
        for (size_t i = 0; i < size; ++i) {
            volatile_ptr[i] = 0;
        }
    }
}

// Encode hash in PHC format
std::string Argon2idHasher::encodeHash(const std::vector<uint8_t>& salt, 
                                      const std::vector<uint8_t>& hash) const {
    // Use Argon2's built-in encoding function
    size_t encoded_len = argon2_encodedlen(
        config_.time_cost,
        config_.memory_cost,
        config_.parallelism,
        config_.salt_length,
        config_.hash_length,
        Argon2_id
    );
    
    std::vector<char> encoded(encoded_len);
    
    int result = argon2_encode(
        encoded.data(),
        encoded_len,
        Argon2_id,
        config_.time_cost,
        config_.memory_cost,
        config_.parallelism,
        salt.data(),
        salt.size(),
        hash.data(),
        hash.size()
    );
    
    if (result != ARGON2_OK) {
        throw Argon2Exception("Encoding failed: " + std::string(argon2_error_message(result)));
    }
    
    return std::string(encoded.data());
}
