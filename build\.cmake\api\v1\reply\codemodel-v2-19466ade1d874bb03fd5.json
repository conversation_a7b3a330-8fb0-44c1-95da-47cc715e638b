{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-901b001ae472ba5986d9.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Argon2idDemo", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "Argon2idHasher::@6890427a1f51a3e7e1df", "jsonFile": "target-Argon2idHasher-Debug-79004f8b2d181c4d088d.json", "name": "Argon2idHasher", "projectIndex": 0}, {"directoryIndex": 0, "id": "argon2_static::@6890427a1f51a3e7e1df", "jsonFile": "target-argon2_static-Debug-bc334da1ac64e2c5f479.json", "name": "argon2_static", "projectIndex": 0}, {"directoryIndex": 0, "id": "demo_argon2id::@6890427a1f51a3e7e1df", "jsonFile": "target-demo_argon2id-Debug-3b6ad558cc83d01ba10e.json", "name": "demo_argon2id", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_argon2id::@6890427a1f51a3e7e1df", "jsonFile": "target-test_argon2id-Debug-2adad412ca33e412ec9d.json", "name": "test_argon2id", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "E:/workspace/vscode/Argon2idDemo/build", "source": "E:/workspace/vscode/Argon2idDemo"}, "version": {"major": 2, "minor": 8}}