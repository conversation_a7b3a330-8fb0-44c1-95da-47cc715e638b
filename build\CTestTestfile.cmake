# CMake generated Testfile for 
# Source directory: E:/workspace/vscode/Argon2idDemo
# Build directory: E:/workspace/vscode/Argon2idDemo/build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(Argon2idTests "E:/workspace/vscode/Argon2idDemo/build/test_argon2id.exe")
set_tests_properties(Argon2idTests PROPERTIES  _BACKTRACE_TRIPLES "E:/workspace/vscode/Argon2idDemo/CMakeLists.txt;101;add_test;E:/workspace/vscode/Argon2idDemo/CMakeLists.txt;0;")
