{"archive": {}, "artifacts": [{"path": "libargon2_static.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_compile_options", "target_compile_definitions", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 104, "parent": 0}, {"command": 2, "file": 0, "line": 19, "parent": 0}, {"command": 2, "file": 0, "line": 21, "parent": 0}, {"command": 3, "file": 0, "line": 56, "parent": 0}, {"command": 4, "file": 0, "line": 31, "parent": 0}, {"command": 4, "file": 0, "line": 32, "parent": 0}, {"command": 5, "file": 0, "line": 50, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-Wpedantic"}, {"backtrace": 3, "fragment": "-Werror"}, {"backtrace": 4, "fragment": "-g"}, {"backtrace": 4, "fragment": "-O0"}], "defines": [{"backtrace": 5, "define": "ARGON2_NO_THREADS=0"}], "includes": [{"backtrace": 6, "path": "E:/workspace/vscode/Argon2idDemo/include"}, {"backtrace": 7, "path": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/include"}, {"backtrace": 8, "path": "E:/workspace/vscode/Argon2idDemo/third_party/argon2/src"}], "language": "C", "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "id": "argon2_static::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/Argon2idDemo"}}, "name": "argon2_static", "nameOnDisk": "libargon2_static.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "third_party/argon2/src/argon2.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "third_party/argon2/src/core.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "third_party/argon2/src/blake2/blake2b.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "third_party/argon2/src/thread.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "third_party/argon2/src/encoding.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "third_party/argon2/src/opt.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}