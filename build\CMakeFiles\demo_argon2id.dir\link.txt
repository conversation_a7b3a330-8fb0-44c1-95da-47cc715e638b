D:\msys64\mingw64\bin\cmake.exe -E rm -f CMakeFiles\demo_argon2id.dir/objects.a
D:\msys64\mingw64\bin\ar.exe qc CMakeFiles\demo_argon2id.dir/objects.a @CMakeFiles\demo_argon2id.dir\objects1.rsp
D:\msys64\mingw64\bin\g++.exe -g -Wl,--whole-archive CMakeFiles\demo_argon2id.dir/objects.a -Wl,--no-whole-archive -o demo_argon2id.exe -Wl,--out-implib,libdemo_argon2id.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\demo_argon2id.dir\linkLibs.rsp
