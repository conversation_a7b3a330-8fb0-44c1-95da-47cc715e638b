# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = D:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\workspace\vscode\Argon2idDemo

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\workspace\vscode\Argon2idDemo\build

# Include any dependencies generated for this target.
include CMakeFiles/argon2_static.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/argon2_static.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/argon2_static.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/argon2_static.dir/flags.make

CMakeFiles/argon2_static.dir/codegen:
.PHONY : CMakeFiles/argon2_static.dir/codegen

CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj: CMakeFiles/argon2_static.dir/flags.make
CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj: CMakeFiles/argon2_static.dir/includes_C.rsp
CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj: E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/argon2.c
CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj: CMakeFiles/argon2_static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj -MF CMakeFiles\argon2_static.dir\third_party\argon2\src\argon2.c.obj.d -o CMakeFiles\argon2_static.dir\third_party\argon2\src\argon2.c.obj -c E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\argon2.c

CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.i"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\argon2.c > CMakeFiles\argon2_static.dir\third_party\argon2\src\argon2.c.i

CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.s"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\argon2.c -o CMakeFiles\argon2_static.dir\third_party\argon2\src\argon2.c.s

CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj: CMakeFiles/argon2_static.dir/flags.make
CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj: CMakeFiles/argon2_static.dir/includes_C.rsp
CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj: E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/core.c
CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj: CMakeFiles/argon2_static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj -MF CMakeFiles\argon2_static.dir\third_party\argon2\src\core.c.obj.d -o CMakeFiles\argon2_static.dir\third_party\argon2\src\core.c.obj -c E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\core.c

CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.i"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\core.c > CMakeFiles\argon2_static.dir\third_party\argon2\src\core.c.i

CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.s"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\core.c -o CMakeFiles\argon2_static.dir\third_party\argon2\src\core.c.s

CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj: CMakeFiles/argon2_static.dir/flags.make
CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj: CMakeFiles/argon2_static.dir/includes_C.rsp
CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj: E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/blake2/blake2b.c
CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj: CMakeFiles/argon2_static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj -MF CMakeFiles\argon2_static.dir\third_party\argon2\src\blake2\blake2b.c.obj.d -o CMakeFiles\argon2_static.dir\third_party\argon2\src\blake2\blake2b.c.obj -c E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\blake2\blake2b.c

CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.i"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\blake2\blake2b.c > CMakeFiles\argon2_static.dir\third_party\argon2\src\blake2\blake2b.c.i

CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.s"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\blake2\blake2b.c -o CMakeFiles\argon2_static.dir\third_party\argon2\src\blake2\blake2b.c.s

CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj: CMakeFiles/argon2_static.dir/flags.make
CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj: CMakeFiles/argon2_static.dir/includes_C.rsp
CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj: E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/thread.c
CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj: CMakeFiles/argon2_static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj -MF CMakeFiles\argon2_static.dir\third_party\argon2\src\thread.c.obj.d -o CMakeFiles\argon2_static.dir\third_party\argon2\src\thread.c.obj -c E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\thread.c

CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.i"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\thread.c > CMakeFiles\argon2_static.dir\third_party\argon2\src\thread.c.i

CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.s"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\thread.c -o CMakeFiles\argon2_static.dir\third_party\argon2\src\thread.c.s

CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj: CMakeFiles/argon2_static.dir/flags.make
CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj: CMakeFiles/argon2_static.dir/includes_C.rsp
CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj: E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/encoding.c
CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj: CMakeFiles/argon2_static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj -MF CMakeFiles\argon2_static.dir\third_party\argon2\src\encoding.c.obj.d -o CMakeFiles\argon2_static.dir\third_party\argon2\src\encoding.c.obj -c E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\encoding.c

CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.i"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\encoding.c > CMakeFiles\argon2_static.dir\third_party\argon2\src\encoding.c.i

CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.s"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\encoding.c -o CMakeFiles\argon2_static.dir\third_party\argon2\src\encoding.c.s

CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj: CMakeFiles/argon2_static.dir/flags.make
CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj: CMakeFiles/argon2_static.dir/includes_C.rsp
CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj: E:/workspace/vscode/Argon2idDemo/third_party/argon2/src/opt.c
CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj: CMakeFiles/argon2_static.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj -MF CMakeFiles\argon2_static.dir\third_party\argon2\src\opt.c.obj.d -o CMakeFiles\argon2_static.dir\third_party\argon2\src\opt.c.obj -c E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\opt.c

CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.i"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\opt.c > CMakeFiles\argon2_static.dir\third_party\argon2\src\opt.c.i

CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.s"
	D:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\workspace\vscode\Argon2idDemo\third_party\argon2\src\opt.c -o CMakeFiles\argon2_static.dir\third_party\argon2\src\opt.c.s

# Object files for target argon2_static
argon2_static_OBJECTS = \
"CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj" \
"CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj" \
"CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj" \
"CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj" \
"CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj" \
"CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj"

# External object files for target argon2_static
argon2_static_EXTERNAL_OBJECTS =

libargon2_static.a: CMakeFiles/argon2_static.dir/third_party/argon2/src/argon2.c.obj
libargon2_static.a: CMakeFiles/argon2_static.dir/third_party/argon2/src/core.c.obj
libargon2_static.a: CMakeFiles/argon2_static.dir/third_party/argon2/src/blake2/blake2b.c.obj
libargon2_static.a: CMakeFiles/argon2_static.dir/third_party/argon2/src/thread.c.obj
libargon2_static.a: CMakeFiles/argon2_static.dir/third_party/argon2/src/encoding.c.obj
libargon2_static.a: CMakeFiles/argon2_static.dir/third_party/argon2/src/opt.c.obj
libargon2_static.a: CMakeFiles/argon2_static.dir/build.make
libargon2_static.a: CMakeFiles/argon2_static.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\workspace\vscode\Argon2idDemo\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking C static library libargon2_static.a"
	$(CMAKE_COMMAND) -P CMakeFiles\argon2_static.dir\cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\argon2_static.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/argon2_static.dir/build: libargon2_static.a
.PHONY : CMakeFiles/argon2_static.dir/build

CMakeFiles/argon2_static.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\argon2_static.dir\cmake_clean.cmake
.PHONY : CMakeFiles/argon2_static.dir/clean

CMakeFiles/argon2_static.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\workspace\vscode\Argon2idDemo E:\workspace\vscode\Argon2idDemo E:\workspace\vscode\Argon2idDemo\build E:\workspace\vscode\Argon2idDemo\build E:\workspace\vscode\Argon2idDemo\build\CMakeFiles\argon2_static.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/argon2_static.dir/depend

