cmake_minimum_required(VERSION 3.16)
project(Argon2idDemo VERSION 1.0.0 LANGUAGES C CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4 /WX)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -Wpedantic -Werror)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-g -O0)
    else()
        add_compile_options(-O3 -DNDEBUG)
    endif()
endif()

# Find required packages
find_package(Threads REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/third_party/argon2/include)

# Add Argon2 library as a subdirectory
# We'll include the Argon2 source directly for better control
set(ARGON2_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/third_party/argon2)

# Argon2 source files
set(ARGON2_SOURCES
    ${ARGON2_SOURCE_DIR}/src/argon2.c
    ${ARGON2_SOURCE_DIR}/src/core.c
    ${ARGON2_SOURCE_DIR}/src/blake2/blake2b.c
    ${ARGON2_SOURCE_DIR}/src/thread.c
    ${ARGON2_SOURCE_DIR}/src/encoding.c
    ${ARGON2_SOURCE_DIR}/src/opt.c
)

# Create Argon2 static library
add_library(argon2_static STATIC ${ARGON2_SOURCES})
target_include_directories(argon2_static PUBLIC 
    ${ARGON2_SOURCE_DIR}/include
    ${ARGON2_SOURCE_DIR}/src
)

# Set Argon2 compile definitions
target_compile_definitions(argon2_static PRIVATE
    ARGON2_NO_THREADS=0
)

# Link threads for Argon2
target_link_libraries(argon2_static Threads::Threads)

# Create our Argon2idHasher library
add_library(Argon2idHasher STATIC
    src/Argon2idHasher.cpp
)

target_include_directories(Argon2idHasher PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

target_link_libraries(Argon2idHasher 
    argon2_static
    Threads::Threads
)

# Create test executable
add_executable(test_argon2id
    tests/test_argon2id.cpp
)

target_link_libraries(test_argon2id
    Argon2idHasher
    argon2_static
    Threads::Threads
)

# Create demo executable
add_executable(demo_argon2id
    demo/demo.cpp
)

target_link_libraries(demo_argon2id
    Argon2idHasher
    argon2_static
    Threads::Threads
)

# Enable testing
enable_testing()
add_test(NAME Argon2idTests COMMAND test_argon2id)

# Installation rules
install(TARGETS Argon2idHasher argon2_static
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES include/Argon2idHasher.h
    DESTINATION include
)

# Print configuration summary
message(STATUS "")
message(STATUS "Configuration Summary:")
message(STATUS "  Project: ${PROJECT_NAME} ${PROJECT_VERSION}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
