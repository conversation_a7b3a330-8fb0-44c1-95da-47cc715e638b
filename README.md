# Argon2id Demo - C++ Password Hashing Library

这是一个完整的Argon2id密码哈希算法的C++类封装实现，提供安全、高效的密码哈希和验证功能。

## 特性

- **安全性**: 使用Argon2id算法，2015年密码哈希竞赛的获胜者
- **可配置**: 支持内存成本、时间成本、并行度等参数配置
- **线程安全**: 支持多线程环境下的并发操作
- **内存安全**: 自动清理敏感数据，防止内存泄露
- **标准格式**: 输出标准的PHC格式哈希字符串
- **现代C++**: 使用C++17特性，遵循RAII原则

## 项目结构

```
Argon2idDemo/
├── include/
│   └── Argon2idHasher.h      # 主要头文件
├── src/
│   └── Argon2idHasher.cpp    # 实现文件
├── tests/
│   └── test_argon2id.cpp     # 单元测试
├── demo/
│   └── demo.cpp              # 演示程序
├── third_party/              # 第三方依赖（自动生成）
│   └── argon2/               # Argon2官方实现
├── CMakeLists.txt            # CMake配置文件
├── setup_argon2.ps1          # 依赖下载脚本
└── README.md                 # 本文件
```

## 快速开始

### 1. 环境要求

- Windows 10/11
- MSYS2/MinGW-w64
- CMake 3.16+
- C++17兼容的编译器

### 2. 下载依赖

运行PowerShell脚本下载Argon2库：

```powershell
.\setup_argon2.ps1
```

### 3. 编译项目

```bash
mkdir build
cd build
cmake .. -G "MSYS Makefiles"
make
```

### 4. 运行演示

```bash
./demo_argon2id
```

### 5. 运行测试

```bash
./test_argon2id
```

## 使用示例

### 基本用法

```cpp
#include "Argon2idHasher.h"

// 创建哈希器（使用默认安全参数）
Argon2idHasher hasher;

// 哈希密码
std::string password = "MySecurePassword123!";
std::string hash = hasher.hash(password);

// 验证密码
bool is_valid = hasher.verify(password, hash);
```

### 自定义配置

```cpp
// 创建自定义配置
Argon2Config config(
    2048,   // 内存成本 (2MB)
    2,      // 时间成本 (2次迭代)
    2,      // 并行度 (2线程)
    16,     // 盐值长度 (16字节)
    32      // 哈希长度 (32字节)
);

// 使用自定义配置创建哈希器
Argon2idHasher hasher(config);
```

### 配置参数说明

- **memory_cost**: 内存使用量（KiB），默认65536（64MB）
- **time_cost**: 迭代次数，默认3
- **parallelism**: 并行线程数，默认4
- **salt_length**: 盐值长度（字节），默认16
- **hash_length**: 哈希输出长度（字节），默认32

## API 参考

### Argon2idHasher 类

#### 构造函数

```cpp
Argon2idHasher();                           // 默认配置
Argon2idHasher(const Argon2Config& config); // 自定义配置
```

#### 主要方法

```cpp
std::string hash(const std::string& password);
bool verify(const std::string& password, const std::string& hash);
Argon2Config getConfig() const;
void setConfig(const Argon2Config& config);
static std::vector<uint8_t> generateSalt(size_t length);
static bool isAvailable();
```

### Argon2Config 结构

```cpp
struct Argon2Config {
    uint32_t memory_cost;    // 内存成本
    uint32_t time_cost;      // 时间成本
    uint32_t parallelism;    // 并行度
    uint32_t salt_length;    // 盐值长度
    uint32_t hash_length;    // 哈希长度
};
```

## 安全建议

1. **参数选择**: 根据你的安全需求和性能要求调整参数
   - 高安全性: memory_cost >= 65536, time_cost >= 3
   - 平衡性能: memory_cost = 4096, time_cost = 3
   - 快速测试: memory_cost = 1024, time_cost = 2

2. **存储**: 直接存储返回的哈希字符串，包含所有必要信息

3. **验证**: 始终使用`verify()`方法验证密码，不要手动比较哈希

## 测试

项目包含完整的单元测试，覆盖以下功能：

- 库可用性检查
- 构造函数测试
- 密码哈希生成
- 密码验证
- 配置管理
- 线程安全
- 性能基准测试

## 许可证

本项目使用MIT许可证。Argon2库使用Apache 2.0许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 参考资料

- [Argon2官方实现](https://github.com/P-H-C/phc-winner-argon2)
- [Argon2规范](https://tools.ietf.org/html/draft-irtf-cfrg-argon2-13)
- [密码哈希竞赛](https://password-hashing.net/)
